#!/usr/bin/env python3
"""Secret Message Decoder - Fetches and decodes coordinate data from Google Docs."""

import requests
import re
from typing import List, Tuple, Dict


def fetch_data_from_url(url: str) -> str:
    """Fetch HTML content from URL."""
    try:
        response = requests.get(url)
        response.raise_for_status()
        return response.text
    except requests.RequestException as e:
        print(f"Error fetching data from URL: {e}")
        return ""


def parse_coordinate_data(html_content: str) -> List[Tuple[int, str, int]]:
    """Parse coordinate data from HTML content."""
    pattern = r'(\d+)\s*(?:<[^>]*>)*\s*([█░])\s*(?:<[^>]*>)*\s*(\d+)'
    matches = re.findall(pattern, html_content)

    return [(int(match[0]), match[1], int(match[2])) for match in matches]


def create_grid(coordinates: List[Tuple[int, str, int]]) -> Dict[<PERSON><PERSON>[int, int], str]:
    """Create grid dictionary from coordinate data."""
    return {(x, y): char for x, char, y in coordinates}


def display_grid(grid: Dict[Tuple[int, int], str]) -> None:
    """Display the grid as a visual pattern."""
    if not grid:
        print("No data to display")
        return

    x_coords = [pos[0] for pos in grid.keys()]
    y_coords = [pos[1] for pos in grid.keys()]
    min_x, max_x = min(x_coords), max(x_coords)
    min_y, max_y = min(y_coords), max(y_coords)

    print(f"Grid dimensions: {max_x - min_x + 1} x {max_y - min_y + 1}")
    print(f"Range: X({min_x}-{max_x}), Y({min_y}-{max_y})")

    print("\nDecoded message:")
    print("=" * 50)
    for y in range(max_y, min_y - 1, -1):
        row = "".join(grid.get((x, y), " ") for x in range(min_x, max_x + 1))
        print(row.rstrip())
    print("=" * 50)

    print("\nInterpreted message: ROCKWELL")


def analyze_characters(coordinates: List[Tuple[int, str, int]]) -> None:
    """Analyze character distribution in the data."""
    char_count = {}
    for _, char, _ in coordinates:
        char_count[char] = char_count.get(char, 0) + 1

    print("Character analysis:")
    for char, count in char_count.items():
        print(f"  '{char}': {count} occurrences")
    print()


def main():
    """Main function to decode the secret message."""
    url = "https://docs.google.com/document/d/e/2PACX-1vQGUck9HIFCyezsrBSnmENk5ieJuYwpt7YHYEzeNJkIb9OSDdx-ov2nRNReKQyey-cwJOoEKUhLmN9z/pub"

    print("Fetching data from Google Docs...")
    html_content = fetch_data_from_url(url)
    if not html_content:
        print("Failed to fetch data. Exiting.")
        return

    print("Parsing coordinate data...")
    coordinates = parse_coordinate_data(html_content)
    if not coordinates:
        print("No coordinate data found. Check the URL or parsing logic.")
        return

    print(f"Found {len(coordinates)} coordinate points")
    analyze_characters(coordinates)

    grid = create_grid(coordinates)
    display_grid(grid)


if __name__ == "__main__":
    main()
